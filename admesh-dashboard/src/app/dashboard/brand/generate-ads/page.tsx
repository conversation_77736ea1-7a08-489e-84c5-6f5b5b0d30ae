"use client";

import React, { useState, useCallback } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Badge } from "@/components/ui/badge";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Label } from "@/components/ui/label";
import { toast } from "sonner";
import { useAuth } from "@/hooks/use-auth";
import {
  Sparkles,
  Image as ImageIcon,
  Loader2,
  Download,
  Copy,
  Wand2,
  Upload,
  X,
  Lightbulb,
  CheckCircle,
  Send,
  Eye,
  RefreshCw,
  Facebook,
  Instagram,
  Linkedin,
  Search,
  ChevronRight,
  Check,
  FileText,
  Target,
  Video
} from "lucide-react";
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from "@/components/ui/dropdown-menu";
import Image from "next/image";
import DashboardFooter from "@/components/DashboardFooter";

interface GeneratedAd {
  id: string;
  type: string;
  content: string;
  headline?: string;
  description?: string;
  cta?: string;
  platform: string;
  format: string;
  format_specs?: {
    width: number;
    height: number;
    aspect_ratio: string;
    name: string;
  };
  dimensions?: {
    width: number;
    height: number;
  };
  media_assets?: Array<{
    id: string;
    type: string;
    url: string;
    metadata: Record<string, any>;
  }>;
  generation_metadata?: Record<string, any>;
}

interface FormData {
  headline: string;
  body: string;
  cta: string;
  targetAudience: string;
  tone: string;
  benefits: string;
  campaignContext: string;
  brandGuidelines: string;
  additionalRequirements: string;
  platform: string;
  format: string;
  numVariations: number;
  generateImages: boolean;
}

interface StepperStep {
  id: string;
  title: string;
  icon: React.ComponentType<{ className?: string }>;
  completed: boolean;
}

export default function GenerateAdsPage() {
  const { user } = useAuth();
  const [currentStep, setCurrentStep] = useState(0);
  const [isGenerating, setIsGenerating] = useState(false);
  const [generatedAds, setGeneratedAds] = useState<GeneratedAd[]>([]);
  const [uploadedImages, setUploadedImages] = useState<File[]>([]);
  const [showManualInputs, setShowManualInputs] = useState(false);
  const [formData, setFormData] = useState<FormData>({
    headline: "",
    body: "",
    cta: "",
    targetAudience: "",
    tone: "professional",
    benefits: "",
    campaignContext: "",
    brandGuidelines: "",
    additionalRequirements: "",
    platform: "facebook",
    format: "feed_post",
    numVariations: 2,
    generateImages: true
  });
  const [selectedPlatforms, setSelectedPlatforms] = useState<string[]>(["facebook", "instagram"]);

  // Platform format definitions
  const platformFormats = {
    facebook: [
      { value: "feed_post", label: "Feed Post", description: "1:1 aspect ratio, 1200x1200px", icon: FileText },
      { value: "story", label: "Story", description: "9:16 aspect ratio, 1080x1920px", icon: ImageIcon },
      { value: "cover_photo", label: "Cover Photo", description: "16:9 aspect ratio, 1640x1025px", icon: ImageIcon }
    ],
    instagram: [
      { value: "post", label: "Post", description: "1:1 aspect ratio, 1080x1080px", icon: ImageIcon },
      { value: "story", label: "Story", description: "9:16 aspect ratio, 1080x1920px", icon: ImageIcon },
      { value: "reels", label: "Reels", description: "9:16 aspect ratio, 1080x1920px", icon: Video }
    ],
    google: [
      { value: "search", label: "Search Ad", description: "Text-based search result", icon: Search }
    ],
    linkedin: [
      { value: "feed_post", label: "Feed Post", description: "1.91:1 aspect ratio", icon: FileText }
    ]
  };

  const platforms = [
    { value: "facebook", label: "Facebook", icon: Facebook, description: "Social media advertising" },
    { value: "instagram", label: "Instagram", icon: Instagram, description: "Visual storytelling platform" },
    { value: "google", label: "Google Ads", icon: Search, description: "Search advertising" },
    { value: "linkedin", label: "LinkedIn", icon: Linkedin, description: "Professional network" }
  ];

  // Stepper configuration
  const steps: StepperStep[] = [
    {
      id: "basic",
      title: "Product Description",
      icon: Wand2,
      completed: !!(formData.body && formData.body.length > 20)
    },
    {
      id: "context",
      title: "Context & Requirements",
      icon: Lightbulb,
      completed: !!(formData.campaignContext || formData.brandGuidelines || formData.additionalRequirements)
    },
    {
      id: "references",
      title: "Reference Images",
      icon: ImageIcon,
      completed: uploadedImages.length > 0
    }
  ];

  const tones = [
    { value: "professional", label: "Professional", description: "Formal and trustworthy" },
    { value: "friendly", label: "Friendly", description: "Warm and approachable" },
    { value: "urgent", label: "Urgent", description: "Time-sensitive and compelling" },
    { value: "creative", label: "Creative", description: "Innovative and artistic" },
    { value: "luxury", label: "Luxury", description: "Premium and exclusive" }
  ];

  const handleInputChange = useCallback((field: keyof FormData, value: string | number | boolean) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
  }, []);

  const handlePlatformChange = useCallback((platform: string) => {
    const defaultFormat = platformFormats[platform as keyof typeof platformFormats]?.[0]?.value || "feed_post";
    setFormData(prev => ({
      ...prev,
      platform,
      format: defaultFormat
    }));
  }, []);

  const handleImageUpload = useCallback((event: React.ChangeEvent<HTMLInputElement>) => {
    const files = Array.from(event.target.files || []);
    const imageFiles = files.filter(file => file.type.startsWith('image/'));
    
    if (imageFiles.length > 0) {
      setUploadedImages(prev => [...prev, ...imageFiles].slice(0, 6)); // Max 6 images
      toast.success(`${imageFiles.length} image(s) uploaded`);
    }
  }, []);

  const removeImage = useCallback((index: number) => {
    setUploadedImages(prev => prev.filter((_, i) => i !== index));
  }, []);

  const generateAds = async () => {
    if (!formData.body || formData.body.length < 20) {
      toast.error("Please provide a detailed description of what you're advertising (at least 20 characters)");
      return;
    }

    if (selectedPlatforms.length === 0) {
      toast.error("Please select at least one platform");
      return;
    }

    setIsGenerating(true);

    try {
      const { getIdToken } = await import("firebase/auth");
      const { auth } = await import("@/lib/firebase");

      if (!auth.currentUser) {
        toast.error("Please log in to generate ads");
        return;
      }

      const token = await getIdToken(auth.currentUser);
      const apiBaseUrl = process.env.NEXT_PUBLIC_API_BASE_URL || 'http://127.0.0.1:8000';

      // Generate ads for all selected platforms
      const allGeneratedAds: GeneratedAd[] = [];

      for (const platform of selectedPlatforms) {
        const requestData = {
          // Main product/service description (this is the key input)
          product_description: formData.body,

          // AI will generate these unless manually overridden
          product_name: formData.headline || "AI_GENERATE",
          target_audience: formData.targetAudience || "AI_GENERATE",
          cta: formData.cta || "AI_GENERATE",
          keywords: formData.benefits || "AI_GENERATE",

          // Platform and style preferences
          platform: platform,
          format: platformFormats[platform as keyof typeof platformFormats]?.[0]?.value || "feed_post",
          tone: formData.tone,
          num_variations: formData.numVariations,

          // Enhanced context for better AI generation
          additional_info: `
AI Instructions: Generate compelling ad content for ${platform} based on the product description.
${formData.campaignContext ? `Campaign Context: ${formData.campaignContext}` : ''}
${formData.brandGuidelines ? `Brand Guidelines: ${formData.brandGuidelines}` : ''}
${formData.additionalRequirements ? `Additional Requirements: ${formData.additionalRequirements}` : ''}
Please create engaging headlines, persuasive copy, and appropriate targeting suggestions optimized for ${platform}.
          `.trim(),

          // Media generation
          ad_type: formData.generateImages ? "multimodal" : "text",
          generate_images: formData.generateImages,
          generate_videos: false,
          image_style: "professional",
          use_brand_assets: true
        };

        const response = await fetch(`${apiBaseUrl}/ad-generation/generate`, {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
            "Authorization": `Bearer ${token}`
          },
          body: JSON.stringify(requestData)
        });

        if (!response.ok) {
          const errorData = await response.json();
          throw new Error(errorData.detail || `Failed to generate ads for ${platform}`);
        }

        const data = await response.json();

        if (data.success && data.ads) {
          allGeneratedAds.push(...data.ads);
        }
      }

      if (allGeneratedAds.length > 0) {
        setGeneratedAds(allGeneratedAds);
        toast.success(`Generated ${allGeneratedAds.length} ads for ${selectedPlatforms.length} platform${selectedPlatforms.length > 1 ? 's' : ''}!`);
      } else {
        throw new Error("No ads were generated");
      }

    } catch (error) {
      console.error("Ad generation error:", error);
      toast.error(error instanceof Error ? error.message : "Failed to generate ads. Please try again.");
    } finally {
      setIsGenerating(false);
    }
  };

  const hasContext = formData.campaignContext || formData.brandGuidelines || formData.additionalRequirements;

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white border-b border-gray-200 sticky top-0 z-10">
        <div className="max-w-7xl mx-auto px-6 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-4">
              <div className="p-2 rounded-xl bg-gradient-to-br from-purple-500 to-blue-600 text-white">
                <Wand2 className="h-6 w-6" />
              </div>
              <div>
                <h1 className="text-2xl font-bold text-gray-900">AI Ad Generator</h1>
                <p className="text-gray-600">Create premium ad creatives with AI</p>
              </div>
            </div>
            <Badge variant="secondary" className="bg-purple-100 text-purple-800">
              <Sparkles className="h-3 w-3 mr-1" />
              Enhanced
            </Badge>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="max-w-7xl mx-auto px-6 py-6">
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 h-[calc(100vh-140px)]">
          {/* Left Panel - Chat Interface */}
          <div className="space-y-4 overflow-y-auto">
            {/* Chat Input Card */}
            <Card className="shadow-sm border-0 bg-white">
              <CardHeader className="pb-4">
                <CardTitle className="flex items-center gap-3">
                  <div className="p-2 rounded-lg bg-purple-100 text-purple-600">
                    <Wand2 className="h-5 w-5" />
                  </div>
                  What do you want to advertise?
                </CardTitle>
                <p className="text-sm text-gray-600">
                  Just describe your product or service - our AI will create amazing ads for you!
                </p>
              </CardHeader>
              <CardContent className="space-y-4">
                {/* Chat-like Input */}
                <div className="relative">
                  <Textarea
                    placeholder="Type here... e.g., Revolutionary AI fitness app that tracks workouts automatically and suggests personalized nutrition plans"
                    value={formData.body}
                    onChange={(e) => handleInputChange("body", e.target.value)}
                    rows={4}
                    className="resize-none border-2 border-gray-200 focus:border-purple-400 rounded-xl p-4 text-base"
                  />
                  <div className="absolute bottom-3 right-3 flex items-center gap-2">
                    <span className="text-xs text-gray-400">
                      {formData.body.length}/500
                    </span>
                    {formData.body.length >= 20 && (
                      <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                    )}
                  </div>
                </div>

                {/* Quick Examples */}
                <div className="space-y-2">
                  <p className="text-xs font-medium text-gray-700">💡 Quick examples:</p>
                  <div className="flex flex-wrap gap-2">
                    {[
                      "AI fitness app with personalized workouts",
                      "Eco-friendly sneakers from ocean plastic",
                      "Premium coffee subscription service",
                      "Time-saving meal prep for busy professionals"
                    ].map((example, index) => (
                      <button
                        key={index}
                        onClick={() => handleInputChange("body", example)}
                        className="text-xs px-3 py-1 bg-gray-100 hover:bg-purple-100 rounded-full transition-colors"
                      >
                        {example}
                      </button>
                    ))}
                  </div>
                </div>

                {/* Platform Selection as Tags */}
                <div className="space-y-3">
                  <Label className="text-sm font-medium text-gray-700">
                    Select platforms to advertise on:
                  </Label>

                  {/* Available Platforms */}
                  <div className="flex flex-wrap gap-2">
                    {platforms.map((platform) => {
                      const PlatformIcon = platform.icon;
                      const isSelected = selectedPlatforms.includes(platform.value);

                      return (
                        <button
                          key={platform.value}
                          onClick={() => {
                            if (isSelected) {
                              setSelectedPlatforms(prev => prev.filter(p => p !== platform.value));
                            } else {
                              setSelectedPlatforms(prev => [...prev, platform.value]);
                            }
                          }}
                          className={`flex items-center gap-2 px-3 py-2 rounded-lg border transition-all ${
                            isSelected
                              ? 'bg-purple-100 border-purple-300 text-purple-700'
                              : 'bg-white border-gray-200 text-gray-600 hover:border-purple-200'
                          }`}
                        >
                          <PlatformIcon className="h-4 w-4" />
                          <span className="text-sm font-medium">{platform.label}</span>
                          {isSelected && (
                            <X className="h-3 w-3 ml-1" />
                          )}
                        </button>
                      );
                    })}
                  </div>

                  {/* Selected Platforms Summary */}
                  {selectedPlatforms.length > 0 && (
                    <div className="flex items-center gap-2 text-sm text-gray-600">
                      <Sparkles className="h-4 w-4 text-purple-500" />
                      <span>
                        Will generate {formData.numVariations} ad{formData.numVariations > 1 ? 's' : ''} for {selectedPlatforms.length} platform{selectedPlatforms.length > 1 ? 's' : ''}
                      </span>
                    </div>
                  )}
                </div>

                {/* Generate Button */}
                <div className="pt-4 border-t">
                  <Button
                    onClick={generateAds}
                    disabled={isGenerating || !formData.body || formData.body.length < 20 || selectedPlatforms.length === 0}
                    className="w-full bg-gradient-to-r from-purple-600 to-blue-600 hover:from-purple-700 hover:to-blue-700 text-white shadow-lg hover:shadow-xl transition-all duration-200"
                    size="lg"
                  >
                    {isGenerating ? (
                      <>
                        <Loader2 className="h-5 w-5 animate-spin mr-2" />
                        Creating your ads...
                      </>
                    ) : (
                      <>
                        <Sparkles className="h-5 w-5 mr-2" />
                        Generate AI Ads ({selectedPlatforms.length} platform{selectedPlatforms.length > 1 ? 's' : ''})
                      </>
                    )}
                  </Button>

                  {(!formData.body || formData.body.length < 20) && (
                    <p className="text-center text-sm text-gray-500 mt-2">
                      Describe your product to generate ads
                    </p>
                  )}

                  {selectedPlatforms.length === 0 && formData.body.length >= 20 && (
                    <p className="text-center text-sm text-gray-500 mt-2">
                      Select at least one platform
                    </p>
                  )}
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Right Panel - Preview */}
          <div className="space-y-4 overflow-y-auto">
            <Card className="shadow-sm border-0 bg-white h-full">
              <CardHeader className="pb-4">
                <CardTitle className="flex items-center gap-3">
                  <div className="p-2 rounded-lg bg-green-100 text-green-600">
                    <Eye className="h-5 w-5" />
                  </div>
                  Live Preview
                </CardTitle>
                <p className="text-sm text-gray-600 mt-1">
                  See how your ad will look on {platforms.find(p => p.value === formData.platform)?.label}
                </p>
              </CardHeader>
              <CardContent className="h-[calc(100%-120px)] flex flex-col">
                {generatedAds.length > 0 ? (
                  <div className="space-y-4 flex-1 overflow-y-auto">
                    {generatedAds.map((ad, index) => (
                      <div key={ad.id} className="space-y-3">
                        <div className="flex items-center justify-between">
                          <Badge variant="outline" className="bg-purple-50 text-purple-700 border-purple-200">
                            Ad #{index + 1}
                          </Badge>
                          <div className="flex gap-2">
                            <Button
                              size="sm"
                              variant="outline"
                              onClick={() => {
                                navigator.clipboard.writeText(ad.content);
                                toast.success("Ad copied!");
                              }}
                            >
                              <Copy className="h-4 w-4" />
                            </Button>
                            <Button size="sm" variant="outline">
                              <Download className="h-4 w-4" />
                            </Button>
                          </div>
                        </div>

                        {/* Platform-specific preview */}
                        <div className="border rounded-lg p-3 bg-gray-50">
                          {formData.platform === 'facebook' && (
                            <div className="bg-white rounded-lg p-3 shadow-sm">
                              <div className="flex items-center gap-2 mb-2">
                                <div className="w-8 h-8 bg-blue-600 rounded-full flex items-center justify-center text-white font-bold text-sm">
                                  B
                                </div>
                                <div>
                                  <div className="font-semibold text-gray-900 text-sm">Your Brand</div>
                                  <div className="text-gray-500 text-xs">Sponsored</div>
                                </div>
                              </div>
                              <div className="text-gray-800 mb-2 text-sm">
                                {ad.content}
                              </div>
                              <button className="bg-blue-600 text-white px-3 py-1 rounded text-xs font-medium">
                                {ad.cta}
                              </button>
                            </div>
                          )}

                          {formData.platform === 'instagram' && (
                            <div className="bg-white rounded-lg overflow-hidden shadow-sm">
                              <div className="flex items-center gap-2 p-3">
                                <div className="w-6 h-6 bg-gradient-to-r from-purple-500 to-pink-500 rounded-full flex items-center justify-center text-white font-bold text-xs">
                                  B
                                </div>
                                <div className="font-semibold text-gray-900 text-sm">yourbrand</div>
                              </div>
                              <div className="bg-gradient-to-br from-purple-400 to-pink-400 aspect-square flex items-center justify-center">
                                <div className="text-white text-center p-2">
                                  <div className="text-xs font-medium">{ad.headline}</div>
                                </div>
                              </div>
                              <div className="p-3">
                                <div className="text-gray-800 text-sm">
                                  <span className="font-semibold">yourbrand</span> {ad.content}
                                </div>
                                <div className="mt-1">
                                  <span className="text-blue-600 font-medium text-xs">{ad.cta}</span>
                                </div>
                              </div>
                            </div>
                          )}

                          {formData.platform === 'google' && (
                            <div className="bg-white rounded-lg p-3 shadow-sm">
                              <div className="text-blue-600 font-medium text-sm hover:underline cursor-pointer">
                                {ad.headline}
                              </div>
                              <div className="text-green-700 text-xs">www.yourbrand.com</div>
                              <div className="text-gray-700 text-xs mt-1">{ad.description}</div>
                            </div>
                          )}

                          {formData.platform === 'linkedin' && (
                            <div className="bg-white rounded-lg p-3 shadow-sm">
                              <div className="flex items-center gap-2 mb-2">
                                <div className="w-8 h-8 bg-blue-700 rounded-full flex items-center justify-center text-white font-bold text-sm">
                                  B
                                </div>
                                <div>
                                  <div className="font-semibold text-gray-900 text-sm">Your Brand</div>
                                  <div className="text-gray-600 text-xs">Company</div>
                                </div>
                              </div>
                              <div className="text-gray-800 mb-2 text-sm">
                                {ad.content}
                              </div>
                              <button className="bg-blue-700 text-white px-3 py-1 rounded text-xs font-medium">
                                {ad.cta}
                              </button>
                            </div>
                          )}
                        </div>
                      </div>
                    ))}
                  </div>
                ) : (
                  <div className="text-center py-8 text-gray-500 flex-1 flex flex-col items-center justify-center">
                    <div className="p-3 rounded-full bg-gray-100 w-12 h-12 mb-3 flex items-center justify-center">
                      <Sparkles className="h-6 w-6 text-gray-300" />
                    </div>
                    <h3 className="font-medium text-gray-900 mb-1">Ready to Generate</h3>
                    <p className="text-sm">Complete the steps and generate your ads</p>
                  </div>
                )}
              </CardContent>
            </Card>
          </div>
        </div>
      </div>

      <DashboardFooter />
    </div>
  );
}
