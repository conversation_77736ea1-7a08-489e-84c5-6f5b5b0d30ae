"use client";

import React, { useState, useCallback } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Badge } from "@/components/ui/badge";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Label } from "@/components/ui/label";
import { toast } from "sonner";
import { useAuth } from "@/hooks/use-auth";
import {
  Sparkles,
  Image as ImageIcon,
  Loader2,
  Download,
  Copy,
  Wand2,
  Upload,
  X,
  Lightbulb,
  CheckCircle,
  Send,
  Eye,
  RefreshCw,
  Facebook,
  Instagram,
  Linkedin,
  Search,
  ChevronRight,
  Check,
  FileText,
  Target,
  Video
} from "lucide-react";
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from "@/components/ui/dropdown-menu";
import Image from "next/image";
import DashboardFooter from "@/components/DashboardFooter";

interface GeneratedAd {
  id: string;
  type: string;
  content: string;
  headline?: string;
  description?: string;
  cta?: string;
  platform: string;
  format: string;
  format_specs?: {
    width: number;
    height: number;
    aspect_ratio: string;
    name: string;
  };
  dimensions?: {
    width: number;
    height: number;
  };
  media_assets?: Array<{
    id: string;
    type: string;
    url: string;
    metadata: Record<string, any>;
  }>;
  generation_metadata?: Record<string, any>;
}

interface FormData {
  headline: string;
  body: string;
  cta: string;
  targetAudience: string;
  tone: string;
  benefits: string;
  campaignContext: string;
  brandGuidelines: string;
  additionalRequirements: string;
  platform: string;
  format: string;
  numVariations: number;
  generateImages: boolean;
}

interface StepperStep {
  id: string;
  title: string;
  icon: React.ComponentType<{ className?: string }>;
  completed: boolean;
}

export default function GenerateAdsPage() {
  const { user } = useAuth();
  const [currentStep, setCurrentStep] = useState(0);
  const [isGenerating, setIsGenerating] = useState(false);
  const [generatedAds, setGeneratedAds] = useState<GeneratedAd[]>([]);
  const [uploadedImages, setUploadedImages] = useState<File[]>([]);
  const [showManualInputs, setShowManualInputs] = useState(false);
  const [formData, setFormData] = useState<FormData>({
    headline: "",
    body: "",
    cta: "",
    targetAudience: "",
    tone: "professional",
    benefits: "",
    campaignContext: "",
    brandGuidelines: "",
    additionalRequirements: "",
    platform: "facebook",
    format: "feed_post",
    numVariations: 1,
    generateImages: false
  });

  // Platform format definitions
  const platformFormats = {
    facebook: [
      { value: "feed_post", label: "Feed Post", description: "1:1 aspect ratio, 1200x1200px", icon: FileText },
      { value: "story", label: "Story", description: "9:16 aspect ratio, 1080x1920px", icon: ImageIcon },
      { value: "cover_photo", label: "Cover Photo", description: "16:9 aspect ratio, 1640x1025px", icon: ImageIcon }
    ],
    instagram: [
      { value: "post", label: "Post", description: "1:1 aspect ratio, 1080x1080px", icon: ImageIcon },
      { value: "story", label: "Story", description: "9:16 aspect ratio, 1080x1920px", icon: ImageIcon },
      { value: "reels", label: "Reels", description: "9:16 aspect ratio, 1080x1920px", icon: Video }
    ],
    google: [
      { value: "search", label: "Search Ad", description: "Text-based search result", icon: Search }
    ],
    linkedin: [
      { value: "feed_post", label: "Feed Post", description: "1.91:1 aspect ratio", icon: FileText }
    ]
  };

  const platforms = [
    { value: "facebook", label: "Facebook", icon: Facebook, description: "Social media advertising" },
    { value: "instagram", label: "Instagram", icon: Instagram, description: "Visual storytelling platform" },
    { value: "google", label: "Google Ads", icon: Search, description: "Search advertising" },
    { value: "linkedin", label: "LinkedIn", icon: Linkedin, description: "Professional network" }
  ];

  // Stepper configuration
  const steps: StepperStep[] = [
    {
      id: "basic",
      title: "Product Description",
      icon: Wand2,
      completed: !!(formData.body && formData.body.length > 20)
    },
    {
      id: "context",
      title: "Context & Requirements",
      icon: Lightbulb,
      completed: !!(formData.campaignContext || formData.brandGuidelines || formData.additionalRequirements)
    },
    {
      id: "references",
      title: "Reference Images",
      icon: ImageIcon,
      completed: uploadedImages.length > 0
    }
  ];

  const tones = [
    { value: "professional", label: "Professional", description: "Formal and trustworthy" },
    { value: "friendly", label: "Friendly", description: "Warm and approachable" },
    { value: "urgent", label: "Urgent", description: "Time-sensitive and compelling" },
    { value: "creative", label: "Creative", description: "Innovative and artistic" },
    { value: "luxury", label: "Luxury", description: "Premium and exclusive" }
  ];

  const handleInputChange = useCallback((field: keyof FormData, value: string | number | boolean) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
  }, []);

  const handlePlatformChange = useCallback((platform: string) => {
    const defaultFormat = platformFormats[platform as keyof typeof platformFormats]?.[0]?.value || "feed_post";
    setFormData(prev => ({
      ...prev,
      platform,
      format: defaultFormat
    }));
  }, []);

  const handleImageUpload = useCallback((event: React.ChangeEvent<HTMLInputElement>) => {
    const files = Array.from(event.target.files || []);
    const imageFiles = files.filter(file => file.type.startsWith('image/'));
    
    if (imageFiles.length > 0) {
      setUploadedImages(prev => [...prev, ...imageFiles].slice(0, 6)); // Max 6 images
      toast.success(`${imageFiles.length} image(s) uploaded`);
    }
  }, []);

  const removeImage = useCallback((index: number) => {
    setUploadedImages(prev => prev.filter((_, i) => i !== index));
  }, []);

  const generateAds = async () => {
    if (!formData.body || formData.body.length < 20) {
      toast.error("Please provide a detailed description of what you're advertising (at least 20 characters)");
      return;
    }

    setIsGenerating(true);

    try {
      const { getIdToken } = await import("firebase/auth");
      const { auth } = await import("@/lib/firebase");

      if (!auth.currentUser) {
        toast.error("Please log in to generate ads");
        return;
      }

      const token = await getIdToken(auth.currentUser);
      const apiBaseUrl = process.env.NEXT_PUBLIC_API_BASE_URL || 'http://127.0.0.1:8000';

      // Prepare the AI-focused request
      const requestData = {
        // Main product/service description (this is the key input)
        product_description: formData.body,

        // AI will generate these unless manually overridden
        product_name: formData.headline || "AI_GENERATE",
        target_audience: formData.targetAudience || "AI_GENERATE",
        cta: formData.cta || "AI_GENERATE",
        keywords: formData.benefits || "AI_GENERATE",

        // Platform and style preferences
        platform: formData.platform,
        format: formData.format,
        tone: formData.tone,
        num_variations: formData.numVariations,

        // Enhanced context for better AI generation
        additional_info: `
AI Instructions: Generate compelling ad content based on the product description.
${formData.campaignContext ? `Campaign Context: ${formData.campaignContext}` : ''}
${formData.brandGuidelines ? `Brand Guidelines: ${formData.brandGuidelines}` : ''}
${formData.additionalRequirements ? `Additional Requirements: ${formData.additionalRequirements}` : ''}
Please create engaging headlines, persuasive copy, and appropriate targeting suggestions.
        `.trim(),

        // Media generation
        ad_type: formData.generateImages ? "multimodal" : "text",
        generate_images: formData.generateImages,
        generate_videos: false,
        image_style: "professional",
        use_brand_assets: true
      };

      const response = await fetch(`${apiBaseUrl}/ad-generation/generate`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          "Authorization": `Bearer ${token}`
        },
        body: JSON.stringify(requestData)
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.detail || "Failed to generate ads");
      }

      const data = await response.json();

      if (data.success && data.ads) {
        setGeneratedAds(data.ads);
        toast.success(data.message || "Ads generated successfully!");
      } else {
        throw new Error("No ads were generated");
      }

    } catch (error) {
      console.error("Ad generation error:", error);
      toast.error(error instanceof Error ? error.message : "Failed to generate ads. Please try again.");
    } finally {
      setIsGenerating(false);
    }
  };

  const hasContext = formData.campaignContext || formData.brandGuidelines || formData.additionalRequirements;

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white border-b border-gray-200 sticky top-0 z-10">
        <div className="max-w-7xl mx-auto px-6 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-4">
              <div className="p-2 rounded-xl bg-gradient-to-br from-purple-500 to-blue-600 text-white">
                <Wand2 className="h-6 w-6" />
              </div>
              <div>
                <h1 className="text-2xl font-bold text-gray-900">AI Ad Generator</h1>
                <p className="text-gray-600">Create premium ad creatives with AI</p>
              </div>
            </div>
            <Badge variant="secondary" className="bg-purple-100 text-purple-800">
              <Sparkles className="h-3 w-3 mr-1" />
              Enhanced
            </Badge>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="max-w-7xl mx-auto px-6 py-6">
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 h-[calc(100vh-140px)]">
          {/* Left Panel - Stepper Form */}
          <div className="space-y-4 overflow-y-auto">
            {/* Stepper Navigation */}
            <Card className="shadow-sm border-0 bg-white">
              <CardContent className="p-4">
                <div className="flex items-center justify-between">
                  {steps.map((step, index) => {
                    const StepIcon = step.icon;
                    const isActive = currentStep === index;
                    const isCompleted = step.completed;

                    return (
                      <div key={step.id} className="flex items-center">
                        <button
                          onClick={() => setCurrentStep(index)}
                          className={`flex items-center gap-2 px-3 py-2 rounded-lg transition-all ${
                            isActive
                              ? 'bg-purple-100 text-purple-700 border border-purple-200'
                              : isCompleted
                                ? 'bg-green-50 text-green-700 hover:bg-green-100'
                                : 'text-gray-500 hover:bg-gray-50'
                          }`}
                        >
                          <div className={`p-1.5 rounded-lg ${
                            isActive
                              ? 'bg-purple-200'
                              : isCompleted
                                ? 'bg-green-200'
                                : 'bg-gray-200'
                          }`}>
                            {isCompleted ? (
                              <Check className="h-4 w-4" />
                            ) : (
                              <StepIcon className="h-4 w-4" />
                            )}
                          </div>
                          <span className="text-sm font-medium hidden sm:block">{step.title}</span>
                        </button>
                        {index < steps.length - 1 && (
                          <ChevronRight className="h-4 w-4 text-gray-300 mx-2" />
                        )}
                      </div>
                    );
                  })}
                </div>
              </CardContent>
            </Card>

            {/* Step Content */}
            {currentStep === 0 && (
              <Card className="shadow-sm border-0 bg-white">
                <CardHeader className="pb-4">
                  <CardTitle className="flex items-center gap-3">
                    <div className="p-2 rounded-lg bg-purple-100 text-purple-600">
                      <Wand2 className="h-5 w-5" />
                    </div>
                    Describe Your Product/Service
                  </CardTitle>
                  <p className="text-sm text-gray-600 mt-2">
                    Just tell us what you're advertising - our AI will handle the rest!
                  </p>
                </CardHeader>
                <CardContent className="space-y-4">
                  {/* Platform Selection */}
                  <div>
                    <Label className="text-sm font-medium text-gray-700 mb-3 block">Platform</Label>
                    <Tabs value={formData.platform} onValueChange={handlePlatformChange} className="w-full">
                      <TabsList className="grid w-full grid-cols-4 bg-gray-100">
                        {platforms.map((platform) => {
                          const PlatformIcon = platform.icon;
                          return (
                            <TabsTrigger
                              key={platform.value}
                              value={platform.value}
                              className="flex items-center gap-2 text-sm"
                            >
                              <PlatformIcon className="h-4 w-4" />
                              <span className="hidden sm:inline">{platform.label}</span>
                            </TabsTrigger>
                          );
                        })}
                      </TabsList>
                    </Tabs>
                  </div>

                  {/* Format Selection */}
                  <div>
                    <Label className="text-sm font-medium text-gray-700">Format</Label>
                    <Select value={formData.format} onValueChange={(value) => handleInputChange("format", value)}>
                      <SelectTrigger className="mt-1.5">
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        {platformFormats[formData.platform as keyof typeof platformFormats]?.map((format) => {
                          const FormatIcon = format.icon;
                          return (
                            <SelectItem key={format.value} value={format.value}>
                              <div className="flex items-center gap-3 py-1">
                                <FormatIcon className="h-4 w-4" />
                                <div>
                                  <div className="font-medium">{format.label}</div>
                                  <div className="text-xs text-gray-500">{format.description}</div>
                                </div>
                              </div>
                            </SelectItem>
                          );
                        })}
                      </SelectContent>
                    </Select>
                  </div>

                  {/* AI-First Input */}
                  <div className="space-y-4">
                    <div>
                      <Label htmlFor="productDescription" className="text-sm font-medium text-gray-700">
                        What are you advertising? *
                      </Label>
                      <Textarea
                        id="productDescription"
                        placeholder="e.g., Revolutionary AI-powered fitness app that tracks your workouts and nutrition automatically..."
                        value={formData.body}
                        onChange={(e) => handleInputChange("body", e.target.value)}
                        rows={3}
                        className="mt-1.5"
                      />
                      <div className="mt-2">
                        <p className="text-xs text-gray-500 mb-2">
                          Our AI will generate compelling headlines, copy, and targeting based on this description
                        </p>
                        <details className="text-xs">
                          <summary className="text-blue-600 cursor-pointer hover:text-blue-700">
                            💡 See example descriptions
                          </summary>
                          <div className="mt-2 space-y-1 text-gray-600 pl-4 border-l-2 border-blue-100">
                            <p>• "Revolutionary AI-powered fitness app that tracks workouts and nutrition automatically"</p>
                            <p>• "Eco-friendly sneakers made from recycled ocean plastic, comfortable for all-day wear"</p>
                            <p>• "Premium coffee subscription delivering freshly roasted beans from small farms worldwide"</p>
                            <p>• "Time-saving meal prep service for busy professionals with dietary restrictions"</p>
                          </div>
                        </details>
                      </div>
                    </div>

                    {/* AI-Generated Preview */}
                    {formData.body && (
                      <div className="p-4 bg-blue-50 rounded-lg border border-blue-200">
                        <div className="flex items-center gap-2 mb-2">
                          <Sparkles className="h-4 w-4 text-blue-600" />
                          <span className="text-sm font-medium text-blue-900">AI Preview</span>
                        </div>
                        <div className="space-y-2 text-sm">
                          <div>
                            <span className="font-medium text-gray-700">Suggested Headline:</span>
                            <p className="text-gray-600 italic">"{formData.body.slice(0, 50)}..." (AI will enhance this)</p>
                          </div>
                          <div>
                            <span className="font-medium text-gray-700">Target Audience:</span>
                            <p className="text-gray-600 italic">AI will analyze and suggest optimal targeting</p>
                          </div>
                        </div>
                      </div>
                    )}

                    {/* Optional Manual Override */}
                    <div className="border-t pt-4">
                      <div className="flex items-center justify-between mb-3">
                        <Label className="text-sm font-medium text-gray-700">Manual Customization (Optional)</Label>
                        <Button
                          type="button"
                          variant="ghost"
                          size="sm"
                          onClick={() => setShowManualInputs(!showManualInputs)}
                          className="text-xs"
                        >
                          {showManualInputs ? 'Hide' : 'Show'} Manual Controls
                        </Button>
                      </div>

                      {showManualInputs && (
                        <div className="space-y-3 p-3 bg-gray-50 rounded-lg">
                          <div>
                            <Label htmlFor="headline" className="text-sm font-medium text-gray-700">
                              Custom Headline (Override AI)
                            </Label>
                            <Input
                              id="headline"
                              placeholder="Leave empty to let AI generate"
                              value={formData.headline}
                              onChange={(e) => handleInputChange("headline", e.target.value)}
                              className="mt-1.5"
                            />
                          </div>
                        </div>
                      )}
                    </div>

                    {/* AI Configuration */}
                    <div className="grid grid-cols-2 gap-4">
                      <div>
                        <Label className="text-sm font-medium text-gray-700">Tone & Style</Label>
                        <Select value={formData.tone} onValueChange={(value) => handleInputChange("tone", value)}>
                          <SelectTrigger className="mt-1.5">
                            <SelectValue />
                          </SelectTrigger>
                          <SelectContent>
                            {tones.map((tone) => (
                              <SelectItem key={tone.value} value={tone.value}>
                                <div>
                                  <div className="font-medium">{tone.label}</div>
                                  <div className="text-xs text-gray-500">{tone.description}</div>
                                </div>
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                      </div>

                      <div>
                        <Label className="text-sm font-medium text-gray-700">Number of Variations</Label>
                        <Select
                          value={formData.numVariations.toString()}
                          onValueChange={(value) => handleInputChange("numVariations", parseInt(value))}
                        >
                          <SelectTrigger className="mt-1.5">
                            <SelectValue />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="1">1 Variation</SelectItem>
                            <SelectItem value="2">2 Variations</SelectItem>
                            <SelectItem value="3">3 Variations</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            )}

            {/* Step 2: Context & Requirements */}
            {currentStep === 1 && (
              <Card className="shadow-sm border-0 bg-blue-50 border-blue-200">
                <CardHeader className="pb-4">
                  <CardTitle className="flex items-center gap-3">
                    <div className="p-2 rounded-lg bg-blue-100 text-blue-600">
                      <Lightbulb className="h-5 w-5" />
                    </div>
                    Context & Requirements
                  </CardTitle>
                  <p className="text-sm text-blue-700 mt-1">
                    Provide rich context to help AI create better, more targeted ads
                  </p>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div>
                    <Label htmlFor="campaignContext" className="text-sm font-medium text-gray-700">
                      Campaign Context
                    </Label>
                    <Textarea
                      id="campaignContext"
                      placeholder="Tell us about your campaign story, product details, objectives..."
                      value={formData.campaignContext}
                      onChange={(e) => handleInputChange("campaignContext", e.target.value)}
                      rows={3}
                      className="mt-1.5 bg-white"
                    />
                  </div>

                  <div>
                    <Label htmlFor="brandGuidelines" className="text-sm font-medium text-gray-700">
                      Brand Guidelines
                    </Label>
                    <Textarea
                      id="brandGuidelines"
                      placeholder="Share your brand voice, style, colors, do's and don'ts..."
                      value={formData.brandGuidelines}
                      onChange={(e) => handleInputChange("brandGuidelines", e.target.value)}
                      rows={3}
                      className="mt-1.5 bg-white"
                    />
                  </div>

                  <div>
                    <Label htmlFor="additionalRequirements" className="text-sm font-medium text-gray-700">
                      Additional Requirements
                    </Label>
                    <Textarea
                      id="additionalRequirements"
                      placeholder="Compliance needs, special requirements, creative direction..."
                      value={formData.additionalRequirements}
                      onChange={(e) => handleInputChange("additionalRequirements", e.target.value)}
                      rows={2}
                      className="mt-1.5 bg-white"
                    />
                  </div>
                </CardContent>
              </Card>
            )}

            {/* Step 3: Reference Images */}
            {currentStep === 2 && (
              <Card className="shadow-sm border-0 bg-white">
                <CardHeader className="pb-4">
                  <CardTitle className="flex items-center gap-3">
                    <div className="p-2 rounded-lg bg-purple-100 text-purple-600">
                      <ImageIcon className="h-5 w-5" />
                    </div>
                    Reference Images
                  </CardTitle>
                  <p className="text-sm text-gray-600 mt-1">
                    Upload competitor ads, style references, or product photos
                  </p>
                </CardHeader>
                <CardContent className="space-y-4">
                  {/* Upload Zone */}
                  <div className="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center hover:border-purple-400 transition-colors">
                    <input
                      type="file"
                      multiple
                      accept="image/*"
                      onChange={handleImageUpload}
                      className="hidden"
                      id="image-upload"
                    />
                    <label htmlFor="image-upload" className="cursor-pointer">
                      <Upload className="h-8 w-8 text-gray-400 mx-auto mb-2" />
                      <p className="text-sm font-medium text-gray-700">
                        Drop images here or click to upload
                      </p>
                      <p className="text-xs text-gray-500 mt-1">
                        PNG, JPG up to 10MB each (max 6 images)
                      </p>
                    </label>
                  </div>

                  {/* Uploaded Images Grid */}
                  {uploadedImages.length > 0 && (
                    <div className="grid grid-cols-2 gap-3">
                      {uploadedImages.map((file, index) => (
                        <div key={index} className="relative group">
                          <div className="aspect-square rounded-lg overflow-hidden bg-gray-100">
                            <Image
                              src={URL.createObjectURL(file)}
                              alt={`Reference ${index + 1}`}
                              width={150}
                              height={150}
                              className="w-full h-full object-cover"
                            />
                          </div>
                          <button
                            onClick={() => removeImage(index)}
                            className="absolute -top-2 -right-2 p-1 bg-red-500 text-white rounded-full opacity-0 group-hover:opacity-100 transition-opacity"
                          >
                            <X className="h-3 w-3" />
                          </button>
                        </div>
                      ))}
                    </div>
                  )}

                  <div className="flex items-center justify-between p-4 bg-purple-50 rounded-lg">
                    <div className="flex items-center gap-3">
                      <input
                        type="checkbox"
                        id="generateImages"
                        checked={formData.generateImages}
                        onChange={(e) => handleInputChange("generateImages", e.target.checked)}
                        className="rounded border-gray-300"
                      />
                      <Label htmlFor="generateImages" className="text-sm font-medium">
                        Generate AI visuals based on context
                      </Label>
                    </div>
                    <Sparkles className="h-5 w-5 text-purple-600" />
                  </div>
                </CardContent>
              </Card>
            )}

            {/* Navigation Buttons */}
            <Card className="shadow-sm border-0 bg-white">
              <CardContent className="p-4">
                <div className="flex items-center justify-between">
                  <Button
                    variant="outline"
                    onClick={() => setCurrentStep(Math.max(0, currentStep - 1))}
                    disabled={currentStep === 0}
                    className="flex items-center gap-2"
                  >
                    <ChevronRight className="h-4 w-4 rotate-180" />
                    Previous
                  </Button>

                  <div className="flex items-center gap-2">
                    {hasContext && (
                      <div className="flex items-center gap-2 px-3 py-1 bg-green-100 text-green-800 rounded-full text-sm">
                        <CheckCircle className="h-4 w-4" />
                        Rich context provided
                      </div>
                    )}
                  </div>

                  {currentStep < steps.length - 1 ? (
                    <Button
                      onClick={() => setCurrentStep(Math.min(steps.length - 1, currentStep + 1))}
                      className="flex items-center gap-2 bg-purple-600 hover:bg-purple-700"
                    >
                      Next
                      <ChevronRight className="h-4 w-4" />
                    </Button>
                  ) : (
                    <Button
                      onClick={generateAds}
                      disabled={isGenerating || !formData.body || formData.body.length < 20}
                      className="flex items-center gap-2 bg-gradient-to-r from-purple-600 to-blue-600 hover:from-purple-700 hover:to-blue-700"
                    >
                      {isGenerating ? (
                        <>
                          <Loader2 className="h-4 w-4 animate-spin" />
                          Generating...
                        </>
                      ) : (
                        <>
                          <Sparkles className="h-4 w-4" />
                          Generate Ads ({formData.numVariations})
                        </>
                      )}
                    </Button>
                  )}
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Right Panel - Preview */}
          <div className="space-y-4 overflow-y-auto">
            <Card className="shadow-sm border-0 bg-white h-full">
              <CardHeader className="pb-4">
                <CardTitle className="flex items-center gap-3">
                  <div className="p-2 rounded-lg bg-green-100 text-green-600">
                    <Eye className="h-5 w-5" />
                  </div>
                  Live Preview
                </CardTitle>
                <p className="text-sm text-gray-600 mt-1">
                  See how your ad will look on {platforms.find(p => p.value === formData.platform)?.label}
                </p>
              </CardHeader>
              <CardContent className="h-[calc(100%-120px)] flex flex-col">
                {generatedAds.length > 0 ? (
                  <div className="space-y-4 flex-1 overflow-y-auto">
                    {generatedAds.map((ad, index) => (
                      <div key={ad.id} className="space-y-3">
                        <div className="flex items-center justify-between">
                          <Badge variant="outline" className="bg-purple-50 text-purple-700 border-purple-200">
                            Ad #{index + 1}
                          </Badge>
                          <div className="flex gap-2">
                            <Button
                              size="sm"
                              variant="outline"
                              onClick={() => {
                                navigator.clipboard.writeText(ad.content);
                                toast.success("Ad copied!");
                              }}
                            >
                              <Copy className="h-4 w-4" />
                            </Button>
                            <Button size="sm" variant="outline">
                              <Download className="h-4 w-4" />
                            </Button>
                          </div>
                        </div>

                        {/* Platform-specific preview */}
                        <div className="border rounded-lg p-3 bg-gray-50">
                          {formData.platform === 'facebook' && (
                            <div className="bg-white rounded-lg p-3 shadow-sm">
                              <div className="flex items-center gap-2 mb-2">
                                <div className="w-8 h-8 bg-blue-600 rounded-full flex items-center justify-center text-white font-bold text-sm">
                                  B
                                </div>
                                <div>
                                  <div className="font-semibold text-gray-900 text-sm">Your Brand</div>
                                  <div className="text-gray-500 text-xs">Sponsored</div>
                                </div>
                              </div>
                              <div className="text-gray-800 mb-2 text-sm">
                                {ad.content}
                              </div>
                              <button className="bg-blue-600 text-white px-3 py-1 rounded text-xs font-medium">
                                {ad.cta}
                              </button>
                            </div>
                          )}

                          {formData.platform === 'instagram' && (
                            <div className="bg-white rounded-lg overflow-hidden shadow-sm">
                              <div className="flex items-center gap-2 p-3">
                                <div className="w-6 h-6 bg-gradient-to-r from-purple-500 to-pink-500 rounded-full flex items-center justify-center text-white font-bold text-xs">
                                  B
                                </div>
                                <div className="font-semibold text-gray-900 text-sm">yourbrand</div>
                              </div>
                              <div className="bg-gradient-to-br from-purple-400 to-pink-400 aspect-square flex items-center justify-center">
                                <div className="text-white text-center p-2">
                                  <div className="text-xs font-medium">{ad.headline}</div>
                                </div>
                              </div>
                              <div className="p-3">
                                <div className="text-gray-800 text-sm">
                                  <span className="font-semibold">yourbrand</span> {ad.content}
                                </div>
                                <div className="mt-1">
                                  <span className="text-blue-600 font-medium text-xs">{ad.cta}</span>
                                </div>
                              </div>
                            </div>
                          )}

                          {formData.platform === 'google' && (
                            <div className="bg-white rounded-lg p-3 shadow-sm">
                              <div className="text-blue-600 font-medium text-sm hover:underline cursor-pointer">
                                {ad.headline}
                              </div>
                              <div className="text-green-700 text-xs">www.yourbrand.com</div>
                              <div className="text-gray-700 text-xs mt-1">{ad.description}</div>
                            </div>
                          )}

                          {formData.platform === 'linkedin' && (
                            <div className="bg-white rounded-lg p-3 shadow-sm">
                              <div className="flex items-center gap-2 mb-2">
                                <div className="w-8 h-8 bg-blue-700 rounded-full flex items-center justify-center text-white font-bold text-sm">
                                  B
                                </div>
                                <div>
                                  <div className="font-semibold text-gray-900 text-sm">Your Brand</div>
                                  <div className="text-gray-600 text-xs">Company</div>
                                </div>
                              </div>
                              <div className="text-gray-800 mb-2 text-sm">
                                {ad.content}
                              </div>
                              <button className="bg-blue-700 text-white px-3 py-1 rounded text-xs font-medium">
                                {ad.cta}
                              </button>
                            </div>
                          )}
                        </div>
                      </div>
                    ))}
                  </div>
                ) : (
                  <div className="text-center py-8 text-gray-500 flex-1 flex flex-col items-center justify-center">
                    <div className="p-3 rounded-full bg-gray-100 w-12 h-12 mb-3 flex items-center justify-center">
                      <Sparkles className="h-6 w-6 text-gray-300" />
                    </div>
                    <h3 className="font-medium text-gray-900 mb-1">Ready to Generate</h3>
                    <p className="text-sm">Complete the steps and generate your ads</p>
                  </div>
                )}
              </CardContent>
            </Card>
          </div>
        </div>
      </div>

      <DashboardFooter />
    </div>
  );
}
