"""
Test enhanced ad generation system with platform-specific formats
"""

# Define platform formats directly for testing (copied from ad_generation.py)
PLATFORM_FORMATS = {
    "instagram": {
        "story": {"width": 1080, "height": 1920, "aspect_ratio": "9:16", "name": "Instagram Story"},
        "post": {"width": 1080, "height": 1080, "aspect_ratio": "1:1", "name": "Instagram Post"},
        "reels": {"width": 1080, "height": 1920, "aspect_ratio": "9:16", "name": "Instagram Reels"}
    },
    "facebook": {
        "feed_post": {"width": 1200, "height": 1200, "aspect_ratio": "1:1", "name": "Facebook Feed Post"},
        "story": {"width": 1080, "height": 1920, "aspect_ratio": "9:16", "name": "Facebook Story"},
        "cover_photo": {"width": 1640, "height": 1025, "aspect_ratio": "16:9", "name": "Facebook Cover Photo"}
    }
}

def validate_platform_format(platform: str, format: str) -> bool:
    """Validate that the format is supported for the given platform"""
    if platform not in PLATFORM_FORMATS:
        return False
    return format in PLATFORM_FORMATS[platform]

def get_format_specs(platform: str, format: str):
    """Get format specifications for a platform and format"""
    if not validate_platform_format(platform, format):
        return PLATFORM_FORMATS["facebook"]["feed_post"]
    return PLATFORM_FORMATS[platform][format]

def get_format_optimization_guidelines(platform: str, format: str) -> str:
    """Get format-specific optimization guidelines"""
    guidelines = {
        "facebook": {
            "feed_post": "Use eye-catching visuals that work well in square format",
            "story": "Design for vertical viewing on mobile devices",
            "cover_photo": "Design for wide landscape format"
        },
        "instagram": {
            "post": "Prioritize high-quality, visually appealing imagery",
            "story": "Design for full-screen vertical experience",
            "reels": "Focus on video-first content with engaging hooks"
        }
    }
    return guidelines.get(platform, {}).get(format, "Optimize content for the selected format")

class TestPlatformFormats:
    """Test platform format specifications"""
    
    def test_platform_formats_structure(self):
        """Test that platform formats are properly structured"""
        assert "facebook" in PLATFORM_FORMATS
        assert "instagram" in PLATFORM_FORMATS
        
        # Test Facebook formats
        fb_formats = PLATFORM_FORMATS["facebook"]
        assert "feed_post" in fb_formats
        assert "story" in fb_formats
        assert "cover_photo" in fb_formats
        
        # Test Instagram formats
        ig_formats = PLATFORM_FORMATS["instagram"]
        assert "post" in ig_formats
        assert "story" in ig_formats
        assert "reels" in ig_formats
    
    def test_format_specifications(self):
        """Test that each format has required specifications"""
        for platform, formats in PLATFORM_FORMATS.items():
            for format_name, specs in formats.items():
                assert "width" in specs
                assert "height" in specs
                assert "aspect_ratio" in specs
                assert "name" in specs
                assert isinstance(specs["width"], int)
                assert isinstance(specs["height"], int)
    
    def test_validate_platform_format(self):
        """Test platform format validation"""
        # Valid combinations
        assert validate_platform_format("facebook", "feed_post") == True
        assert validate_platform_format("instagram", "story") == True
        assert validate_platform_format("instagram", "reels") == True
        
        # Invalid combinations
        assert validate_platform_format("facebook", "reels") == False
        assert validate_platform_format("instagram", "cover_photo") == False
        assert validate_platform_format("invalid_platform", "story") == False
    
    def test_get_format_specs(self):
        """Test getting format specifications"""
        # Valid format
        specs = get_format_specs("instagram", "story")
        assert specs["width"] == 1080
        assert specs["height"] == 1920
        assert specs["aspect_ratio"] == "9:16"
        
        # Invalid format should return default
        specs = get_format_specs("invalid", "invalid")
        assert specs == PLATFORM_FORMATS["facebook"]["feed_post"]
    
    def test_get_format_optimization_guidelines(self):
        """Test format optimization guidelines"""
        guidelines = get_format_optimization_guidelines("instagram", "story")
        assert isinstance(guidelines, str)
        assert len(guidelines) > 0
        assert "vertical" in guidelines.lower() or "story" in guidelines.lower()

class TestAdGenerationRequest:
    """Test enhanced ad generation request validation"""

    def test_request_validation_logic(self):
        """Test request validation logic"""
        # Test valid platform/format combinations
        assert validate_platform_format("instagram", "story") == True
        assert validate_platform_format("facebook", "feed_post") == True

        # Test invalid combinations
        assert validate_platform_format("instagram", "cover_photo") == False
        assert validate_platform_format("facebook", "reels") == False

class TestFormatOptimization:
    """Test format-specific optimization"""
    
    def test_instagram_story_optimization(self):
        """Test Instagram story specific guidelines"""
        guidelines = get_format_optimization_guidelines("instagram", "story")

        # Should contain story-specific advice
        assert "vertical" in guidelines.lower() or "full-screen" in guidelines.lower()
    
    def test_facebook_cover_optimization(self):
        """Test Facebook cover photo specific guidelines"""
        guidelines = get_format_optimization_guidelines("facebook", "cover_photo")

        # Should contain cover photo specific advice
        assert "landscape" in guidelines.lower() or "wide" in guidelines.lower()
    
    def test_instagram_reels_optimization(self):
        """Test Instagram reels specific guidelines"""
        guidelines = get_format_optimization_guidelines("instagram", "reels")

        # Should contain reels-specific advice
        assert "video" in guidelines.lower() or "engaging" in guidelines.lower()

if __name__ == "__main__":
    # Run basic tests
    test_formats = TestPlatformFormats()
    test_formats.test_platform_formats_structure()
    test_formats.test_format_specifications()
    test_formats.test_validate_platform_format()
    test_formats.test_get_format_specs()
    test_formats.test_get_format_optimization_guidelines()
    
    test_request = TestAdGenerationRequest()
    test_request.test_request_validation_logic()
    
    test_optimization = TestFormatOptimization()
    test_optimization.test_instagram_story_optimization()
    test_optimization.test_facebook_cover_optimization()
    test_optimization.test_instagram_reels_optimization()
    
    print("✅ All enhanced ad generation tests passed!")
