#!/usr/bin/env python3
"""
Test script for media storage functionality
"""

import asyncio
import sys
import os

# Add the project root to the Python path
sys.path.insert(0, os.path.abspath(os.path.dirname(__file__)))

from api.services.storage_service import StorageService
from firebase.config import initialize_firebase

async def test_media_storage():
    """Test the media storage functionality"""
    
    print("🧪 Testing Media Storage Functionality")
    print("=" * 50)
    
    # Initialize Firebase
    try:
        initialize_firebase()
        print("✅ Firebase initialized successfully")
    except Exception as e:
        print(f"❌ Failed to initialize Firebase: {e}")
        return
    
    # Initialize storage service
    try:
        storage_service = StorageService()
        print("✅ Storage service initialized successfully")
    except Exception as e:
        print(f"❌ Failed to initialize storage service: {e}")
        return
    
    # Test data with mock media assets
    test_brand_id = "test_brand_123"
    test_session_id = "test_session_456"
    
    # Sample ad data with media assets (using a sample image URL)
    test_ads = [
        {
            "id": "test_ad_1",
            "platform": "facebook",
            "headline": "Test Ad Headline",
            "description": "This is a test ad description",
            "cta": "Learn More",
            "content": "Test ad content",
            "type": "image",
            "format": "image",
            "media_assets": [
                {
                    "id": "test_media_1",
                    "type": "image",
                    "url": "https://picsum.photos/1200/628",  # Sample image URL
                    "metadata": {
                        "source": "test",
                        "generated_at": "2024-01-01T00:00:00Z"
                    },
                    "created_at": "2024-01-01T00:00:00Z"
                }
            ],
            "generation_metadata": {
                "model_used": "test",
                "style": "professional"
            },
            "platform_adaptations": {}
        }
    ]
    
    test_metadata = {
        "prompt": "Test ad generation",
        "platforms": ["facebook"],
        "image_style": "professional",
        "ad_type": "image"
    }
    
    print(f"\n📤 Testing ad storage with media files...")
    print(f"Brand ID: {test_brand_id}")
    print(f"Session ID: {test_session_id}")
    print(f"Number of ads: {len(test_ads)}")
    print(f"Media assets per ad: {len(test_ads[0]['media_assets'])}")
    
    try:
        # Test saving ads with media
        result = await storage_service.save_ads_to_storage(
            brand_id=test_brand_id,
            session_id=test_session_id,
            ads=test_ads,
            metadata=test_metadata
        )
        
        if result["success"]:
            print("✅ Successfully saved ads with media files!")
            print(f"   Batch ID: {result['batch_id']}")
            print(f"   Storage Path: {result['storage_path']}")
            print(f"   Total Saved: {result['total_saved']}")
            
            batch_id = result['batch_id']
            
            # Test retrieving saved ads
            print(f"\n📥 Testing ad retrieval...")
            
            retrieved_result = await storage_service.get_saved_ads(
                brand_id=test_brand_id,
                limit=10
            )
            
            if retrieved_result["success"]:
                print("✅ Successfully retrieved saved ads!")
                print(f"   Total Batches: {retrieved_result['total_batches']}")
                print(f"   Total Ads: {retrieved_result['total_ads']}")
                
                # Check if media URLs are accessible
                if retrieved_result["batches"]:
                    batch = retrieved_result["batches"][0]
                    if batch["ads"]:
                        ad = batch["ads"][0]
                        if ad.get("media_assets"):
                            media_asset = ad["media_assets"][0]
                            print(f"   Media URL: {media_asset.get('url', 'No URL')}")
                            print(f"   Storage Path: {media_asset.get('storage_path', 'No storage path')}")
                            print(f"   File Size: {media_asset.get('file_size', 'Unknown')} bytes")
            else:
                print("❌ Failed to retrieve saved ads")
            
            # Test storage stats
            print(f"\n📊 Testing storage statistics...")
            
            stats_result = await storage_service.get_storage_stats(test_brand_id)
            
            if stats_result["success"]:
                print("✅ Successfully retrieved storage stats!")
                print(f"   Total Batches: {stats_result['total_batches']}")
                print(f"   Total Ads: {stats_result['total_ads']}")
                print(f"   Total Media Files: {stats_result['total_media_files']}")
                print(f"   Storage Used: {stats_result['storage_used_mb']} MB")
            else:
                print("❌ Failed to retrieve storage stats")
            
            # Clean up - delete the test batch
            print(f"\n🧹 Cleaning up test data...")
            
            delete_result = await storage_service.delete_ad_batch(
                brand_id=test_brand_id,
                batch_id=batch_id
            )
            
            if delete_result["success"]:
                print("✅ Successfully cleaned up test data!")
                print(f"   Deleted Files: {delete_result['total_deleted']}")
            else:
                print("❌ Failed to clean up test data")
                
        else:
            print("❌ Failed to save ads with media files")
            
    except Exception as e:
        print(f"❌ Error during testing: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(test_media_storage())
